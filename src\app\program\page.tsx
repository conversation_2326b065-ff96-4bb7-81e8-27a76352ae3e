'use client'

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useProgramWithCalculationsAndCache } from '@/hooks/useProgramWithCalculationsAndCache'
import {
  ProgramDescription,
  ProgramErrorState,
  CompletedProgramState,
  PartialDataError,
} from '@/components'
import { Alert } from '@/components/ui/Alert'
import type { ProgramStats } from '@/types'
import { ProgramStats as ProgramStatsComponent } from '@/components/program/ProgramStats'
import { FloatingCTAButton } from '@/components/ui'
import { AuthGuard } from '@/components/AuthGuard'
import { ProgramErrorBoundary } from '@/components/program/ProgramErrorBoundary'
import {
  measureProgramPageLoad,
  reportProgramMetrics,
  observeProgramPerformance,
  clearPerformanceMarks,
} from '@/utils/programPerformance'
import { useHaptic } from '@/utils/haptic'
import { usePullToRefresh } from '@/hooks/usePullToRefresh'
import { PullToRefreshIndicator } from '@/components/PullToRefreshIndicator'
import { SuccessAnimation } from '@/components/SuccessAnimation'
import { useUserInfo } from '@/hooks/useUserInfo'
import { useUserStats } from '@/hooks/useUserStats'
import {
  ScreenReaderAnnouncer,
  useScreenReaderAnnouncer,
} from '@/components/ui/ScreenReaderAnnouncer'
import { handleErrorWithRecovery } from '@/utils/errorHandling'
import {
  startProgressiveLoadingMonitoring,
  checkProgressiveLoadingPerformance,
} from '@/utils/progressiveLoadingMonitor'
// import { WelcomeCard } from '@/components/WelcomeCard'
// import { useWelcomeCard } from '@/hooks/useWelcomeCard'
// import { useAuthStore } from '@/stores/authStore'

// No program state component
function NoProgramState() {
  const router = useRouter()
  const haptic = useHaptic('light')

  return (
    <div className="flex flex-col items-center justify-center h-full p-4">
      <div className="text-center space-y-4">
        <div className="text-text-tertiary mb-4">
          <svg
            className="w-16 h-16 mx-auto"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        </div>
        <h2 className="text-xl font-semibold text-text-primary">
          No Program Assigned
        </h2>
        <p className="text-text-secondary">
          You haven't selected a workout program yet.
        </p>
        <button
          onClick={haptic.withHandler(() => router.push('/programs'))}
          className="px-6 py-3 bg-brand-primary text-text-inverse rounded-theme hover:bg-brand-primary/90 transition-colors shadow-theme-md hover:shadow-theme-lg"
        >
          Browse Programs
        </button>
      </div>
    </div>
  )
}

function ProgramPageContent() {
  const router = useRouter()
  const [showSuccess, setShowSuccess] = React.useState(false)
  const [showStatsLoaded, setShowStatsLoaded] = React.useState(false)
  const { announcement, announce } = useScreenReaderAnnouncer()
  // const user = useAuthStore((state) => state.user)

  // Start performance monitoring
  useEffect(() => {
    measureProgramPageLoad.start()

    // Start progressive loading monitoring
    startProgressiveLoadingMonitoring()

    // Set up performance observer
    const cleanup = observeProgramPerformance(() => {
      // Performance metrics are now handled by the observer
    })

    // Cleanup on unmount
    return () => {
      cleanup()
      clearPerformanceMarks()
    }
  }, [])

  // Fetch program data with enhanced calculations and cache support
  const {
    program,
    progress,
    isLoading,
    isRefreshing,
    error,
    refetch,
    hasPartialDataError,
  } = useProgramWithCalculationsAndCache()

  // Fetch user stats using the new implementation
  const {
    stats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats,
  } = useUserStats()

  // Fetch welcome card data - temporarily commented out
  // const {
  //   recoveryInfo,
  //   programName: welcomeProgramName,
  //   nextWorkoutName,
  //   isLoading: isLoadingWelcome,
  //   refetch: refetchWelcome,
  // } = useWelcomeCard()

  // Debug logging for welcome card
  // useEffect(() => {
  //   if (process.env.NODE_ENV === 'development') {
  //     // eslint-disable-next-line no-console
  //     console.log('[ProgramPage] Welcome card data:', {
  //       hasRecoveryInfo: !!recoveryInfo,
  //       recoveryInfo,
  //       isLoadingWelcome,
  //       programName: welcomeProgramName,
  //       nextWorkoutName,
  //     })
  //   }
  // }, [recoveryInfo, isLoadingWelcome, welcomeProgramName, nextWorkoutName])

  // Debug logging for stats in program page
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && stats) {
      // eslint-disable-next-line no-console
      console.log('[ProgramPage] Stats received from useUserStats:', {
        weekStreak: stats.weekStreak,
        workoutsCompleted: stats.workoutsCompleted,
        lbsLifted: stats.lbsLifted,
      })
    }
  }, [stats])

  // Show "Stats loaded!" message when stats finish loading
  useEffect(() => {
    let timer: NodeJS.Timeout | undefined
    if (!isLoadingStats && stats && stats.weekStreak >= 0) {
      setShowStatsLoaded(true)
      // Hide the message after 1 second
      timer = setTimeout(() => {
        setShowStatsLoaded(false)
      }, 1000)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [isLoadingStats, stats])

  // Fetch user info with progressive loading
  const { refetch: refetchUserInfo } = useUserInfo()

  // Mark page as interactive when data is loaded
  useEffect(() => {
    if (!isLoading && program) {
      measureProgramPageLoad.end()
      reportProgramMetrics()
      // Announce when data is loaded
      announce('Program data loaded successfully')

      // Check progressive loading performance
      checkProgressiveLoadingPerformance()
    }
  }, [isLoading, program, announce])

  // Haptic feedback
  const haptic = useHaptic('medium')

  // Handle navigation to workout
  const handleContinueToWorkout = haptic.withHandler(() => {
    router.push('/workout')
  })

  // Pull to refresh - refreshes both program data and user info
  const {
    isRefreshing: isPullRefreshing,
    pullDistance,
    isPulling,
  } = usePullToRefresh({
    onRefresh: async () => {
      haptic.trigger()
      announce('Refreshing program data')
      try {
        // Refresh program data, user info, stats, and welcome data in parallel
        await Promise.all([
          refetch(),
          refetchUserInfo(),
          refetchStats(),
          // refetchWelcome(),
        ])
        setShowSuccess(true)
        announce('Program data refreshed successfully')
      } catch (error) {
        // Handle errors with recovery
        await handleErrorWithRecovery(error, {
          retry: async () => {
            await Promise.all([
              refetch(),
              refetchUserInfo(),
              refetchStats(),
              // refetchWelcome(),
            ])
          },
          onCacheCleared: () => {
            announce('Cache cleared due to errors. Retrying...')
          },
        })
      }
    },
    enabled: !isLoading,
    container: '[data-testid="scroll-container"]',
  })

  // Show critical error state only if there's an error and no partial data
  if (error && !hasPartialDataError && !program) {
    return <ProgramErrorState error={error} onRetry={() => refetch()} />
  }

  // Special states - only show after initial load attempt
  if (!isLoading && !isRefreshing) {
    // No program state
    if (!program || program === null) {
      return <NoProgramState />
    }

    // Invalid program data
    if (program && (!program.name || program.id === undefined)) {
      const validationError = new Error(
        'Invalid program data: missing required fields'
      )
      return (
        <ProgramErrorState error={validationError} onRetry={() => refetch()} />
      )
    }

    // Completed program
    if (program && progress && progress.percentage === 100) {
      // Convert UserStats to ProgramStats format for CompletedProgramState
      const programStats: ProgramStats | undefined = stats
        ? {
            totalWorkoutsCompleted: stats.workoutsCompleted,
            totalVolume: stats.lbsLifted,
            consecutiveWeeks: stats.weekStreak,
            personalRecords: 0, // Not available in UserStats
            averageWorkoutTime: 0, // Not available in UserStats
            lastWorkoutDate: '', // Not available in UserStats
            recoveryDays: 0, // Not available in UserStats
            coachRecommendation: 'Train', // Default value
            bodyWeight: undefined, // Not available in UserStats
          }
        : undefined

      return <CompletedProgramState program={program} stats={programStats} />
    }
  }

  // Always render the main page structure immediately
  // Components will handle their own loading states

  return (
    <div
      data-testid="program-overview-page"
      className="h-full flex flex-col bg-bg-primary relative"
    >
      {/* Screen reader announcements */}
      <ScreenReaderAnnouncer message={announcement} priority="polite" />

      {/* Pull to refresh indicator */}
      <PullToRefreshIndicator
        pullDistance={pullDistance}
        threshold={80}
        isRefreshing={isPullRefreshing}
        isPulling={isPulling}
      />

      {/* Success animation */}
      <SuccessAnimation
        show={showSuccess}
        onComplete={() => setShowSuccess(false)}
      />
      {/* Scrollable content */}
      <div
        data-testid="scroll-container"
        className="flex-1 overflow-y-auto"
        role="main"
        aria-label="Program overview"
      >
        <div className="p-4 pb-24">
          <div className="mx-auto max-w-lg space-y-6">
            {/* Background refresh indicator */}
            {isRefreshing && (
              <div className="text-xs text-text-tertiary text-center animate-pulse">
                Checking for updates...
              </div>
            )}

            {/* Partial data error alert */}
            {hasPartialDataError && (
              <PartialDataError
                failedData={[...(!progress ? ['Progress tracking'] : [])]}
                onRetry={() => refetch()}
              />
            )}

            {/* Stats error alert */}
            {statsError && (
              <Alert
                variant="error"
                action={{
                  label: 'Retry',
                  onClick: () => refetchStats(),
                }}
              >
                Failed to load statistics. Tap to retry.
              </Alert>
            )}

            {/* Welcome Card with recovery status - temporarily commented out */}
            {/* {recoveryInfo && (
                <WelcomeCard
                  userName={user?.firstName}
                  recoveryInfo={recoveryInfo}
                  programName={welcomeProgramName || program?.name}
                  nextWorkoutName={nextWorkoutName || undefined}
                  onStartWorkout={handleContinueToWorkout}
                  onAcknowledgeRest={() => {
                    haptic.trigger()
                    announce('Rest day acknowledged')
                  }}
                  isLoading={isLoadingWelcome}
                />
              )} */}
            {/* Stat cards - show 3 metrics vertically with card-based design */}
            <ProgramStatsComponent
              stats={stats}
              isLoadingStats={isLoadingStats}
              showStatsLoaded={showStatsLoaded}
            />
            {/* Program description - handle null/undefined gracefully */}
            {program?.description && (
              <ProgramDescription
                description={program.description}
                maxLines={4}
              />
            )}

            {/* Loading workout message - always rendered to prevent layout shift */}
            <p
              className={`text-center text-text-secondary mt-6 transition-opacity duration-300 ${
                isLoading || isLoadingStats
                  ? 'opacity-100 animate-pulse'
                  : 'opacity-0'
              }`}
            >
              {isLoading || isLoadingStats ? (
                'Loading your workout...'
              ) : (
                <span className="text-brand-accent">Loaded</span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Floating CTA button */}
      <FloatingCTAButton onClick={handleContinueToWorkout} />
    </div>
  )
}

export default function ProgramPage() {
  return (
    <AuthGuard>
      <ProgramErrorBoundary>
        <ProgramPageContent />
      </ProgramErrorBoundary>
    </AuthGuard>
  )
}
