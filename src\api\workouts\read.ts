/**
 * Workout API - Read Operations
 *
 * Functions for fetching workout data
 */

import { apiClient } from '../client'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'
import type {
  WorkoutTemplateGroupModel,
  WorkoutTemplateModel,
  ExerciseModel,
  RecommendationModel,
  GetUserWorkoutLogAverageResponse,
  TimeZoneInfoModel,
  WorkoutLogSerieModel,
} from '@/types'
import { handleWorkoutResponse } from './utils'
import {
  getUserWorkoutProgramInfo,
  getWorkoutDetails as getWorkoutDetailsService,
} from '@/services/api/workout'

/**
 * Get user's assigned program and next workout with timezone info
 * @returns User's program info with next workout
 */
export async function getUserProgramInfo(): Promise<GetUserWorkoutLogAverageResponse | null> {
  // Get user's timezone
  const timeZoneInfo: TimeZoneInfoModel = {
    TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
    Offset: new Date().getTimezoneOffset() / -60,
    IsDaylightSaving: false, // Simple implementation
  }

  try {
    // Use V2 endpoint that returns correct WorkoutCount
    const response = await apiClient.post(
      '/api/WorkoutLog/GetUserWorkoutLogAverageWithUserStatsV2',
      timeZoneInfo
    )

    logger.log('[getUserProgramInfo] API Response:', {
      status: response.status,
      statusCode: response.data?.StatusCode,
      hasResult: !!response.data?.Result,
      hasData: !!response.data?.Data,
      dataKeys: response.data ? Object.keys(response.data) : [],
      fullResponse: response.data,
    })

    // The API returns data wrapped in { StatusCode, Result, ErrorMessage }
    if (response.data.StatusCode === 200 && response.data.Result) {
      return response.data.Result as GetUserWorkoutLogAverageResponse
    }

    // Fallback for other response structures
    const data = response.data.Data || response.data

    return data as GetUserWorkoutLogAverageResponse
  } catch (error) {
    logger.error('Failed to get user program info:', error)
    throw error
  }
}

/**
 * Get user's actual workout with exercises
 * @returns Array of workout templates with exercises
 */
export async function getUserWorkout(): Promise<WorkoutTemplateModel[]> {
  const MAX_RETRIES = 3
  const INITIAL_DELAY = 1000 // 1 second

  let lastError: unknown

  // Use a helper function to handle retries
  const executeWithRetry = async (): Promise<WorkoutTemplateModel[]> => {
    for (let attempt = 0; attempt < MAX_RETRIES; attempt += 1) {
      try {
        // Use extended timeout for this endpoint as it can be slow
        // eslint-disable-next-line no-await-in-loop
        const response = await apiClient.post(
          '/api/Workout/GetUserWorkout',
          {},
          {
            timeout: 30000 + attempt * 15000, // 30s, 45s, 60s timeouts
          }
        )

        return handleWorkoutResponse(response)
      } catch (error) {
        lastError = error
        logger.warn(
          `GetUserWorkout attempt ${attempt + 1} failed:`,
          error instanceof Error ? error.message : 'Unknown error'
        )

        // If it's the last attempt, throw
        if (attempt === MAX_RETRIES - 1) {
          throw error instanceof Error ? error : new Error('Unknown error')
        }

        // Wait before retrying with exponential backoff
        const delay = new Promise((resolve) =>
          setTimeout(resolve, INITIAL_DELAY * Math.pow(2, attempt))
        )
        // eslint-disable-next-line no-await-in-loop
        await delay
      }
    }

    // Should never reach here, but TypeScript needs this
    const finalError =
      lastError instanceof Error
        ? lastError
        : new Error('Failed to get user workout')
    throw finalError
  }

  return executeWithRetry()
}

/**
 * Get today's workout using the mobile app workflow
 * @returns Array of workout template groups
 */
export async function getTodaysWorkout(): Promise<WorkoutTemplateGroupModel[]> {
  try {
    // Step 1: Get user's workout program info with timezone
    const programInfo = await getUserWorkoutProgramInfo()

    logger.log('[getTodaysWorkout] getUserWorkoutProgramInfo result:', {
      hasProgramInfo: !!programInfo,
      hasNextWorkout:
        !!programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate,
      nextWorkoutId:
        programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate?.Id,
      nextWorkoutLabel:
        programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate
          ?.Label,
    })

    // Check if we have a next workout template
    const nextWorkout =
      programInfo?.GetUserProgramInfoResponseModel?.NextWorkoutTemplate
    if (!nextWorkout || !nextWorkout.Id) {
      logger.warn('[getTodaysWorkout] No next workout template found')
      return []
    }

    // Step 2: Get full workout details with exercises
    const workoutDetails = await getWorkoutDetailsService(nextWorkout.Id)

    if (!workoutDetails) {
      logger.warn(
        '[getTodaysWorkout] Failed to get workout details for ID:',
        nextWorkout.Id
      )
      return []
    }

    // Normalize field names (handle both "Exercises" and "Exercices")
    const workoutWithExercices = workoutDetails as WorkoutTemplateModel & {
      Exercices?: ExerciseModel[]
    }
    if ('Exercices' in workoutWithExercices && !workoutDetails.Exercises) {
      workoutDetails.Exercises = workoutWithExercices.Exercices || []
    }

    logger.log('[getTodaysWorkout] Got workout details:', {
      workoutId: workoutDetails.Id,
      label: workoutDetails.Label,
      exerciseCount: workoutDetails.Exercises?.length || 0,
    })

    // Wrap in WorkoutTemplateGroupModel structure
    const programLabel =
      programInfo?.GetUserProgramInfoResponseModel?.RecommendedProgram?.Label ||
      nextWorkout.Label

    return [
      {
        Id:
          programInfo?.GetUserProgramInfoResponseModel?.RecommendedProgram
            ?.Id || 1,
        Label: programLabel,
        WorkoutTemplates: [workoutDetails],
        IsFeaturedProgram: false,
        UserId: getCurrentUserEmail() || '',
        IsSystemExercise: false,
        RequiredWorkoutToLevelUp: 0,
        ProgramId:
          programInfo?.GetUserProgramInfoResponseModel?.RecommendedProgram
            ?.Id || 1,
      },
    ]
  } catch (error) {
    logger.warn(
      '[getTodaysWorkout] Mobile app workflow failed, falling back to GetUserWorkoutTemplateGroup:',
      error
    )

    try {
      // Fallback to the original endpoint
      const response = await apiClient.post(
        '/api/Workout/GetUserWorkoutTemplateGroup',
        {}
      )

      // Handle various response formats
      let workoutGroups: WorkoutTemplateGroupModel[] | null = null

      // 1. Check for StatusCode/Result wrapper
      if (
        response.data &&
        'StatusCode' in response.data &&
        response.data.StatusCode === 200 &&
        'Result' in response.data
      ) {
        workoutGroups = response.data.Result
      }
      // 2. Check for Data wrapper
      else if (response.data && 'Data' in response.data) {
        workoutGroups = response.data.Data
      }
      // 3. Check for direct array response
      else if (Array.isArray(response.data)) {
        workoutGroups = response.data
      }
      // 4. Check if response.data itself is a valid workout group
      else if (
        response.data &&
        typeof response.data === 'object' &&
        'WorkoutTemplates' in response.data
      ) {
        workoutGroups = [response.data as WorkoutTemplateGroupModel]
      }

      // Validate the response
      if (
        workoutGroups &&
        Array.isArray(workoutGroups) &&
        workoutGroups.length > 0
      ) {
        logger.log(
          '[getTodaysWorkout] Fallback successful with workout groups:',
          {
            count: workoutGroups.length,
            firstGroupLabel: workoutGroups[0]?.Label,
          }
        )
        return workoutGroups
      }

      throw new Error('GetUserWorkoutTemplateGroup returned unexpected format')
    } catch (fallbackError) {
      logger.warn(
        '[getTodaysWorkout] Fallback also failed, using getUserWorkout:',
        fallbackError
      )

      // Final fallback to getUserWorkout
      const workouts = await getUserWorkout()

      // Wrap workouts in a template group structure
      return [
        {
          Id: 1,
          Label: "Today's Workout",
          WorkoutTemplates: workouts,
          IsFeaturedProgram: false,
          UserId: getCurrentUserEmail() || '',
          IsSystemExercise: false,
          RequiredWorkoutToLevelUp: 0,
          ProgramId: 1,
        },
      ]
    }
  }
}

/**
 * Get specific workout details
 * @param workoutId - ID of the workout to fetch
 * @returns Workout template with exercises
 */
export async function getWorkoutDetails(
  workoutId: string
): Promise<WorkoutTemplateModel> {
  const response = await apiClient.get(`/api/Workout/GetWorkout/${workoutId}`)

  // Handle the response (might be wrapped)
  const data = response.data.Result || response.data.Data || response.data

  return data as WorkoutTemplateModel
}

/**
 * @deprecated Use getExerciseRecommendation from @/services/api/workout instead
 * Get exercise recommendation with warm-ups
 * @param exerciseId - ID of the exercise
 * @param isFromServer - Whether to force server-side calculation
 * @returns Exercise recommendation with sets, reps, weight, and warm-ups
 */
export async function getExerciseRecommendation(
  exerciseId: number
): Promise<RecommendationModel | null> {
  console.warn(
    '⚠️ Using deprecated getExerciseRecommendation from @/api/workouts/read. Use @/services/api/workout instead.'
  )

  // Redirect to the new function
  const { getExerciseRecommendation: newGetExerciseRecommendation } =
    await import('@/services/api/workout')
  const { getUserSettings } = await import('@/services/userSettings')

  try {
    const username = getCurrentUserEmail()
    if (!username) {
      throw new Error('User not authenticated')
    }

    const userSettings = await getUserSettings()

    // Convert old API signature to new API signature
    const request = {
      Username: username,
      ExerciseId: exerciseId,
      WorkoutId: 0, // Default workout ID - this should be provided by the caller
      SetStyle: 'Normal', // Default to Normal
      IsFlexibility: false, // Default to false
      IsQuickMode: userSettings.isQuickMode,
      LightSessionDays: userSettings.lightSessionDays,
      SwapedExId: undefined,
      IsStrengthPhashe: userSettings.isStrengthPhase,
      IsFreePlan: userSettings.isFreePlan,
      IsFirstWorkoutOfStrengthPhase: userSettings.isFirstWorkoutOfStrengthPhase,
      VersionNo: 1,
    }

    return await newGetExerciseRecommendation(request)
  } catch (error) {
    logger.error('Failed to get exercise recommendation (deprecated):', error)
    return null
  }
}

/**
 * Get workout history
 * @param limit - Maximum number of workouts to return
 * @returns Array of historical workouts
 */
export async function getWorkoutHistory(
  limit: number = 10
): Promise<WorkoutTemplateModel[]> {
  const response = await apiClient.get('/api/Workout/GetWorkoutHistory', {
    params: { limit },
  })

  const data = response.data.Result || response.data.Data || response.data
  return Array.isArray(data) ? data : []
}

/**
 * Get exercise alternatives/swaps
 * @param exerciseId - ID of the exercise to find alternatives for
 * @returns Array of alternative exercises
 */
export async function getExerciseAlternatives(
  exerciseId: number
): Promise<ExerciseModel[]> {
  const response = await apiClient.get(
    `/api/Exercise/GetAlternatives/${exerciseId}`
  )
  const data = response.data.Result || response.data.Data || response.data
  return Array.isArray(data) ? data : []
}

/**
 * Get all sets for a specific exercise
 * @param exerciseId - ID of the exercise
 * @returns Array of workout log series
 */
export async function getExerciseSets(
  exerciseId: number
): Promise<WorkoutLogSerieModel[]> {
  const username = getCurrentUserEmail()
  if (!username) {
    return []
  }

  const response = await apiClient.get(
    `/api/WorkoutLog/GetExerciseSets/${username}/${exerciseId}`
  )
  const data = response.data.Result || response.data.Data || response.data
  return Array.isArray(data) ? data : []
}

/**
 * Get user workout program timezone info
 * @returns Timezone info and program data
 */
export async function getUserWorkoutProgramTimeZoneInfo(): Promise<{
  timeZoneInfo: TimeZoneInfoModel
  programData?: unknown
}> {
  const timeZoneInfo: TimeZoneInfoModel = {
    TimeZoneId: Intl.DateTimeFormat().resolvedOptions().timeZone,
    Offset: new Date().getTimezoneOffset() / -60,
    IsDaylightSaving: false,
  }

  try {
    const response = await apiClient.post(
      '/api/WorkoutLog/GetUserWorkoutProgramTimeZoneInfo',
      { standardName: timeZoneInfo.TimeZoneId }
    )

    logger.log('[GetUserWorkoutProgramTimeZoneInfo] response:', {
      status: response.status,
      statusCode: response.data?.statusCode,
      hasResult: response.data?.hasResult,
      hasData: response.data?.hasData,
      dataKeys: response.data?.dataKeys,
      fullResponse: response.data,
    })

    return {
      timeZoneInfo,
      programData: response.data,
    }
  } catch (error) {
    logger.error('Failed to get timezone info:', error)
    return { timeZoneInfo }
  }
}
