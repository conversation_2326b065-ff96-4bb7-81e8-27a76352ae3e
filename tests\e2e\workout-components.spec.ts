import { test, expect } from '@playwright/test'

test.describe('Workout Components @critical', () => {
  const TEST_USER = {
    email: '<EMAIL>',
    password: 'Dr123456',
  }

  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.getByLabel('Email').fill(TEST_USER.email)
    await page.getByLabel('Password').fill(TEST_USER.password)
    await page.getByRole('button', { name: /log in/i }).click()
    await page.waitForURL('/program')
  })

  test.describe('Exercise Card Component', () => {
    test('should display exercise information correctly @critical', async ({
      page,
    }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Wait for exercise cards to load
      const exerciseCard = page.locator('[data-testid="exercise-item"]').first()
      await expect(exerciseCard).toBeVisible()

      // Verify exercise card contains required elements
      await expect(
        exerciseCard.locator('[data-testid="exercise-name"]')
      ).toBeVisible()
      await expect(
        exerciseCard.locator('[data-testid="sets-info"]')
      ).toBeVisible()
      await expect(
        exerciseCard.locator('[data-testid="muscle-group"]')
      ).toBeVisible()

      // Exercise should be clickable
      await expect(exerciseCard).toHaveAttribute('role', 'button')
    })

    test('should show completion status @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Complete an exercise
      const exerciseCard = page.locator('[data-testid="exercise-item"]').first()
      await exerciseCard.click()

      // Complete a set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Mark exercise as complete
      const completeButton = page.getByRole('button', {
        name: /complete exercise|finish/i,
      })
      if (await completeButton.isVisible({ timeout: 5000 })) {
        await completeButton.click()
      }

      // Return to workout overview
      await page.goto('/workout')

      // Exercise should show completed status
      await expect(exerciseCard).toHaveAttribute('data-completed', 'true')
      const checkmark = exerciseCard.locator('[data-testid="completed-icon"]')
      await expect(checkmark).toBeVisible()
    })
  })

  test.describe('Set Input Component', () => {
    test('should handle weight and reps input @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Test weight input
      const weightInput = page.locator('input[name="weight"]')
      await expect(weightInput).toBeVisible()
      await expect(weightInput).toHaveAttribute('type', 'number')
      await expect(weightInput).toHaveAttribute('inputmode', 'decimal')

      await weightInput.fill('100.5')
      await expect(weightInput).toHaveValue('100.5')

      // Test reps input
      const repsInput = page.locator('input[name="reps"]')
      await expect(repsInput).toBeVisible()
      await expect(repsInput).toHaveAttribute('type', 'number')
      await expect(repsInput).toHaveAttribute('inputmode', 'numeric')

      await repsInput.fill('12')
      await expect(repsInput).toHaveValue('12')
    })

    test('should show validation errors @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Try to save without filling inputs
      await page.getByRole('button', { name: /save|done/i }).click()

      // Should show validation errors
      const weightInput = page.locator('input[name="weight"]')
      const repsInput = page.locator('input[name="reps"]')

      await expect(weightInput).toHaveAttribute('aria-invalid', 'true')
      await expect(repsInput).toHaveAttribute('aria-invalid', 'true')

      // Error messages should be visible
      await expect(page.locator('[data-testid="weight-error"]')).toBeVisible()
      await expect(page.locator('[data-testid="reps-error"]')).toBeVisible()
    })
  })

  test.describe('Rest Timer Component', () => {
    test('should display countdown timer @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Complete a set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Skip RIR if present
      const rirSelector = page.locator('[data-testid="rir-selector"]')
      if (await rirSelector.isVisible({ timeout: 3000 })) {
        await page.locator('[data-testid="rir-2"]').click()
        await page.getByRole('button', { name: /confirm|continue/i }).click()
      }

      // Timer should be visible
      const restTimer = page.locator('[data-testid="rest-timer"]')
      await expect(restTimer).toBeVisible()

      // Timer display should be visible
      const timerDisplay = page.locator('[data-testid="timer-display"]')
      await expect(timerDisplay).toBeVisible()

      // Should have skip button
      const skipButton = page.getByRole('button', { name: /skip/i })
      await expect(skipButton).toBeVisible()

      // Timer should be counting down
      const initialTime = await timerDisplay.textContent()
      await page.waitForTimeout(2000)
      const currentTime = await timerDisplay.textContent()
      expect(initialTime).not.toBe(currentTime)
    })

    test('should allow skipping rest timer @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Complete a set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Skip RIR if present
      const rirSelector = page.locator('[data-testid="rir-selector"]')
      if (await rirSelector.isVisible({ timeout: 3000 })) {
        await page.locator('[data-testid="rir-2"]').click()
        await page.getByRole('button', { name: /confirm|continue/i }).click()
      }

      // Skip rest timer
      await page.getByRole('button', { name: /skip/i }).click()

      // Should navigate to next set or exercise
      await expect(page.locator('[data-testid="set-input"]')).toBeVisible()
    })
  })

  test.describe('RIR Picker Component', () => {
    test('should display RIR options @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Complete a set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // RIR picker should be visible
      const rirSelector = page.locator('[data-testid="rir-selector"]')
      await expect(rirSelector).toBeVisible()

      // Should have RIR options (0-5)
      const rirOptions = await Promise.all(
        Array.from({ length: 6 }, (_, i) =>
          page.locator(`[data-testid="rir-${i}"]`).isVisible()
        )
      )
      rirOptions.forEach((isVisible) => {
        expect(isVisible).toBe(true)
      })

      // Should show description
      await expect(
        page.locator('[data-testid="rir-description"]')
      ).toBeVisible()
    })

    test('should select RIR value @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Complete a set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Select RIR 2
      await page.locator('[data-testid="rir-2"]').click()

      // Option should be selected
      await expect(page.locator('[data-testid="rir-2"]')).toHaveAttribute(
        'aria-selected',
        'true'
      )

      // Confirm selection
      await page.getByRole('button', { name: /confirm|continue/i }).click()

      // Should proceed to rest timer or next set
      const restTimer = page.locator('[data-testid="rest-timer"]')
      const setInput = page.locator('[data-testid="set-input"]')
      await expect(restTimer.or(setInput)).toBeVisible()
    })

    test('should display RIR picker with theme-aware colors @theme', async ({
      page,
    }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Test with subtle-depth theme
      await page.evaluate(() => {
        document.documentElement.setAttribute('data-theme', 'subtle-depth')
        localStorage.setItem('theme', 'subtle-depth')
      })

      // Click first exercise
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Complete first set
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Check if RIR dialog appears
      const rirTitle = page.getByText(/How many more reps could you do?/i)

      if (await rirTitle.isVisible({ timeout: 3000 })) {
        // Verify the dialog has proper contrast
        const rirDialog = page.getByRole('dialog')
        const dialogBox = await rirDialog.boundingBox()

        if (dialogBox) {
          // Take screenshot for visual verification
          await page.screenshot({
            path: 'test-results/rir-picker-theme.png',
            clip: dialogBox,
          })
        }

        // Verify buttons are visible and clickable
        const rirOption = page.getByText('Could do 1-2 more')
        await expect(rirOption).toBeVisible()

        // Click to select
        await rirOption.click()
      }
    })
  })

  test.describe('Workout Progress Component', () => {
    test('should display overall workout progress @critical', async ({
      page,
    }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Progress indicator should be visible
      const progressBar = page.locator('[data-testid="workout-progress"]')
      await expect(progressBar).toBeVisible()

      // Should show exercise count
      const exerciseCount = page.locator('[data-testid="exercise-count"]')
      await expect(exerciseCount).toContainText(/\d+\s*\/\s*\d+/)

      // Complete an exercise
      await page.locator('[data-testid="exercise-item"]').first().click()
      await page.locator('input[name="weight"]').fill('100')
      await page.locator('input[name="reps"]').fill('10')
      await page.getByRole('button', { name: /save|done/i }).click()

      // Mark as complete
      const completeButton = page.getByRole('button', {
        name: /complete exercise|finish/i,
      })
      if (await completeButton.isVisible({ timeout: 5000 })) {
        await completeButton.click()
      }

      // Progress should update
      await page.goto('/workout')
      const updatedProgress = await progressBar.getAttribute('aria-valuenow')
      expect(Number(updatedProgress)).toBeGreaterThan(0)
    })
  })

  test.describe('Loading States', () => {
    test('should show skeleton loaders while loading @critical', async ({
      page,
    }) => {
      // Slow down network to see loading states
      await page.route('**/*', (route) => {
        setTimeout(() => route.continue(), 1000)
      })

      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()

      // Should show skeleton loaders
      const skeletons = page.locator('[data-testid="skeleton-loader"]')
      await expect(skeletons.first()).toBeVisible()

      // Wait for content to load
      await expect(
        page.locator('[data-testid="exercise-item"]').first()
      ).toBeVisible({ timeout: 10000 })

      // Skeletons should be gone
      await expect(skeletons.first()).not.toBeVisible()
    })
  })

  test.describe('Error States', () => {
    test('should handle API errors gracefully @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Intercept API calls and make them fail
      await page.route('**/api/**', (route) => route.abort('failed'))

      // Try to load exercise
      await page.locator('[data-testid="exercise-item"]').first().click()

      // Should show error state
      const errorMessage = page.locator('[data-testid="error-message"]')
      await expect(errorMessage).toBeVisible()

      // Should have retry button
      const retryButton = page.getByRole('button', { name: /retry|try again/i })
      await expect(retryButton).toBeVisible()

      // Remove route interception
      await page.unroute('**/api/**')

      // Click retry
      await retryButton.click()

      // Should load successfully
      await expect(page.locator('[data-testid="set-input"]')).toBeVisible({
        timeout: 10000,
      })
    })
  })

  test.describe('Workout Completion', () => {
    test('should show success screen on completion @critical', async ({
      page,
    }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Look for complete workout button
      const completeWorkoutButton = page.getByRole('button', {
        name: /complete workout|finish workout/i,
      })

      if (await completeWorkoutButton.isVisible({ timeout: 5000 })) {
        await completeWorkoutButton.click()

        // Should show success screen
        const successScreen = page.locator('[data-testid="workout-success"]')
        await expect(successScreen).toBeVisible()

        // Should show success message
        await expect(
          page.locator('[data-testid="success-message"]')
        ).toContainText(/great job|well done|completed/i)

        // Should have continue button
        const continueButton = page.getByRole('button', {
          name: /continue|done|back to program/i,
        })
        await expect(continueButton).toBeVisible()

        // Click continue
        await continueButton.click()

        // Should navigate back to program
        await expect(page).toHaveURL('/program')
      }
    })
  })

  test.describe('Mobile Interactions', () => {
    test('should have proper touch targets @critical', async ({ page }) => {
      await page
        .getByRole('button', { name: /start workout|today's workout/i })
        .click()
      await page.waitForURL('/workout')

      // Check all interactive elements
      const buttons = page.locator('button, [role="button"]')
      const count = await buttons.count()

      let tooSmallTargets = 0
      const minSize = 44 // Minimum touch target size

      const buttonChecks = await Promise.all(
        Array.from({ length: Math.min(count, 10) }, async (_, i) => {
          const button = buttons.nth(i)
          const isVisible = await button.isVisible()
          if (isVisible) {
            const box = await button.boundingBox()
            return box && (box.width < minSize || box.height < minSize)
          }
          return false
        })
      )

      tooSmallTargets = buttonChecks.filter(Boolean).length

      // All touch targets should meet minimum size
      expect(tooSmallTargets).toBe(0)
    })
  })
})
