/**
 * Recommendation-related actions for workout store
 */

import type { WorkoutState } from './types'
import type { RecommendationModel } from '@/types'
import { logger } from '@/utils/logger'
import { getCurrentUserEmail } from '@/lib/auth-utils'

export const createRecommendationActions = (
  set: (
    partial:
      | Partial<WorkoutState>
      | ((state: WorkoutState) => Partial<WorkoutState>)
  ) => void,
  get: () => WorkoutState
) => ({
  loadExerciseRecommendation: async (exerciseId: number) => {
    const {
      currentWorkout,
      loadingStates,
      setCachedExerciseRecommendation,
      exercises,
    } = get()

    // Check if already loading
    if (loadingStates.get(exerciseId)) {
      return
    }

    // Get workout ID
    const workoutId = currentWorkout?.Id
    if (!workoutId) {
      logger.warn('No current workout ID for recommendation request')
      return
    }

    // Find the exercise to get its SetStyle and IsFlexibility
    const exercise = exercises.find((ex) => ex.Id === exerciseId)
    if (!exercise) {
      logger.warn(`Exercise ${exerciseId} not found in current workout`)
      return
    }

    // Get user from auth state
    const username = getCurrentUserEmail()
    if (!username) {
      logger.warn('No username for recommendation request')
      return
    }

    // Generate cache key
    const cacheKey = get().getCacheKey(username, exerciseId, workoutId)

    // Set loading state
    set((state) => {
      const newLoadingStates = new Map(state.loadingStates)
      newLoadingStates.set(exerciseId, true)
      return { loadingStates: newLoadingStates }
    })

    try {
      // Import getExerciseRecommendation and getUserSettings dynamically to avoid circular dependencies
      const { getExerciseRecommendation } = await import(
        '@/services/api/workout'
      )
      const { getUserSettings } = await import('@/services/userSettings')

      // Get user settings for the recommendation request
      const userSettings = await getUserSettings()

      const recommendation = await getExerciseRecommendation({
        Username: username,
        ExerciseId: exerciseId,
        WorkoutId: workoutId,
        SetStyle: exercise.SetStyle,
        IsFlexibility: exercise.IsFlexibility,
        IsQuickMode: userSettings.isQuickMode,
        LightSessionDays: userSettings.lightSessionDays,
        SwapedExId: undefined,
        IsStrengthPhashe: userSettings.isStrengthPhase, // Note: API has typo
        IsFreePlan: userSettings.isFreePlan,
        IsFirstWorkoutOfStrengthPhase:
          userSettings.isFirstWorkoutOfStrengthPhase,
        VersionNo: 1,
      })

      if (recommendation) {
        // Store in memory map
        set((state) => {
          const newRecommendations = new Map(state.exerciseRecommendations)
          newRecommendations.set(cacheKey, recommendation)
          return { exerciseRecommendations: newRecommendations }
        })

        // Store in cache
        setCachedExerciseRecommendation(exerciseId, recommendation)
      }
    } catch (error) {
      // Store error
      set((state) => {
        const newErrors = new Map(state.errors)
        newErrors.set(
          exerciseId,
          error instanceof Error
            ? error
            : new Error('Failed to load recommendation')
        )
        return { errors: newErrors }
      })
      logger.error(
        `Failed to load recommendation for exercise ${exerciseId}:`,
        error
      )
    } finally {
      // Clear loading state
      set((state) => {
        const newLoadingStates = new Map(state.loadingStates)
        newLoadingStates.delete(exerciseId)
        return { loadingStates: newLoadingStates }
      })
    }
  },

  loadAllExerciseRecommendations: async () => {
    const { exercises, currentWorkout, loadExerciseRecommendation } = get()

    if (!exercises || exercises.length === 0) {
      logger.warn('No exercises to load recommendations for')
      return
    }

    if (!currentWorkout?.Id) {
      logger.warn('No current workout ID for recommendations request')
      return
    }

    logger.info(
      `Loading recommendations for ${exercises.length} exercises in parallel`
    )

    // Load all recommendations in parallel
    const recommendationPromises = exercises.map((exercise) =>
      loadExerciseRecommendation(exercise.Id)
    )

    try {
      await Promise.all(recommendationPromises)
      logger.info('All exercise recommendations loaded successfully')
    } catch (error) {
      logger.error('Failed to load some exercise recommendations:', error)
      // Individual errors are already handled in loadExerciseRecommendation
    }
  },

  getCacheKey: (
    userId: string,
    exerciseId: number,
    workoutId: number
  ): string => {
    return `${userId}-${exerciseId}-${workoutId}`
  },

  // Selectors
  getExerciseRecommendation: (
    exerciseId: number
  ): RecommendationModel | undefined => {
    const { exerciseRecommendations, currentWorkout } = get()

    // Get user from auth state
    const username = getCurrentUserEmail()
    if (!username || !currentWorkout?.Id) return undefined

    const cacheKey = get().getCacheKey(username, exerciseId, currentWorkout.Id)
    return exerciseRecommendations.get(cacheKey)
  },

  isExerciseLoading: (exerciseId: number): boolean => {
    return get().loadingStates.get(exerciseId) || false
  },
})
