import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { WorkoutOverview } from '../WorkoutOverview'
import * as useWorkoutModule from '@/hooks/useWorkout'
import {
  createMockUseWorkoutReturn,
  createMockWorkoutTemplateGroup,
  createMockExerciseWorkSetsFromExercise,
} from './WorkoutOverview.test.helpers'
import type { ExerciseModel } from '@/types'

// Mock next/navigation
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}))

// Mock pull to refresh hook
vi.mock('@/hooks/usePullToRefresh', () => ({
  usePullToRefresh: () => ({
    pullDistance: 0,
    isPulling: false,
    isRefreshing: false,
  }),
}))

describe('WorkoutOverview - Floating CTA Button', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock useWorkout with valid workout data
    const mockExercises = [
      { Id: 1, Label: 'Bench Press' } as ExerciseModel,
      { Id: 2, Label: 'Shoulder Press' } as ExerciseModel,
    ]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = mockExercises.map((ex) =>
      createMockExerciseWorkSetsFromExercise(ex)
    )

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      exercises,
      exerciseWorkSetsModels: exercises,
      hasInitialData: true,
      isLoadingWorkout: false,
      workoutError: null,
    })

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)
  })

  it('should render floating CTA button with correct positioning', () => {
    const { container } = render(<WorkoutOverview />)

    const floatingContainer = container.querySelector(
      '[data-testid="floating-cta-container"]'
    )
    expect(floatingContainer).toBeInTheDocument()
    expect(floatingContainer).toHaveClass('fixed')
    expect(floatingContainer).toHaveClass('bottom-6')
    expect(floatingContainer).toHaveClass('left-0')
    expect(floatingContainer).toHaveClass('right-0')
    expect(floatingContainer).toHaveClass('z-50')
  })

  it('should show Start Workout label when no workout session', () => {
    render(<WorkoutOverview />)

    const button = screen.getByText('Start Workout')
    expect(button).toBeInTheDocument()
  })

  it('should show Continue Workout label when workout session exists', () => {
    // Set up data with workout session to trigger Continue Workout
    const mockExercises = [{ Id: 1, Label: 'Bench Press' } as ExerciseModel]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = mockExercises.map((ex) =>
      createMockExerciseWorkSetsFromExercise(ex)
    )

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      exercises,
      exerciseWorkSetsModels: exercises,
      hasInitialData: true,
      isLoadingWorkout: false,
      workoutError: null,
      workoutSession: { id: 1, startTime: new Date() },
    })

    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)

    render(<WorkoutOverview />)

    const button = screen.getByText('Continue Workout')
    expect(button).toBeInTheDocument()
  })

  it('should show Finish and save workout when session has completed sets', () => {
    const mockExercises = [{ Id: 1, Label: 'Bench Press' } as ExerciseModel]

    const mockWorkoutGroup = createMockWorkoutTemplateGroup({
      WorkoutTemplates: [
        {
          Id: 1,
          Label: 'Upper Body Day',
          IsSystemExercise: true,
          UserId: '',
          Exercises: mockExercises,
          WorkoutSettingsModel: {},
        },
      ],
    })

    const exercises = [
      createMockExerciseWorkSetsFromExercise(
        { Id: 1, Label: 'Bench Press' } as ExerciseModel,
        {
          WorkSet1HasValue: true,
          WorkSet1RepsValue: 10,
          WorkSet1Value: 100,
        }
      ),
    ]

    const mockReturn = createMockUseWorkoutReturn({
      todaysWorkout: [mockWorkoutGroup],
      workoutSession: { id: 1, startTime: new Date() },
      exercises,
      exerciseWorkSetsModels: exercises,
      hasInitialData: true,
      isLoadingWorkout: false,
      workoutError: null,
    })
    vi.spyOn(useWorkoutModule, 'useWorkout').mockReturnValue(mockReturn)

    render(<WorkoutOverview />)

    const button = screen.getByText('Finish and save workout')
    expect(button).toBeInTheDocument()
  })

  it('should have pill-shaped floating button design', () => {
    render(<WorkoutOverview />)

    const button = screen.getByText('Start Workout')
    expect(button).toHaveClass('rounded-full')
    expect(button).toHaveClass('px-6')
    expect(button).toHaveClass('py-4')
    expect(button).toHaveClass('min-h-[56px]')
  })

  it('should not have fixed full-width bottom section', () => {
    const { container } = render(<WorkoutOverview />)

    // Should not have the old fixed bottom section
    const oldFixedSection = container.querySelector(
      '.fixed.bottom-0.left-0.right-0.bg-bg-primary'
    )
    expect(oldFixedSection).not.toBeInTheDocument()

    // Should not have full width button wrapper
    const fullWidthWrapper = container.querySelector('.w-full.mx-auto.max-w-lg')
    expect(fullWidthWrapper).not.toBeInTheDocument()
  })
})
