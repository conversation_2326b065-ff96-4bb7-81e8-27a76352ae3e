import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { vi } from 'vitest'
import { FloatingCTAButton } from '../FloatingCTAButton'

describe('FloatingCTAButton', () => {
  const mockOnClick = vi.fn()

  beforeEach(() => {
    mockOnClick.mockClear()
  })

  it('should render with default props', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button', { name: /open workout/i })
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Open Workout')
  })

  it('should render with custom label', () => {
    render(<FloatingCTAButton onClick={mockOnClick} label="Start Exercise" />)

    const button = screen.getByRole('button')
    expect(button).toHaveTextContent('Start Exercise')
  })

  it('should render with custom aria-label', () => {
    render(
      <FloatingCTAButton
        onClick={mockOnClick}
        ariaLabel="Begin your workout session"
      />
    )

    const button = screen.getByRole('button', {
      name: 'Begin your workout session',
    })
    expect(button).toBeInTheDocument()
  })

  it('should call onClick when clicked', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    fireEvent.click(button)

    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('should have centered floating positioning styles', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const wrapper = container.querySelector(
      '[data-testid="floating-cta-container"]'
    )
    expect(wrapper).toHaveClass('fixed')
    expect(wrapper).toHaveClass('bottom-6')
    expect(wrapper).toHaveClass('left-0')
    expect(wrapper).toHaveClass('right-0')
    expect(wrapper).toHaveClass('z-50')
    expect(wrapper).toHaveClass('px-4')
  })

  it('should have pill-shaped button with proper sizing', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('px-6')
    expect(button).toHaveClass('py-4')
    expect(button).toHaveClass('rounded-full')
    expect(button).toHaveClass('min-h-[56px]') // Mobile touch target
  })

  it('should use theme colors', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('bg-brand-primary')
    expect(button).toHaveClass('text-text-inverse')
  })

  it('should have shadow and hover effects', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('shadow-theme-xl')
    expect(button).toHaveClass('hover:shadow-theme-2xl')
    expect(button).toHaveClass('hover:bg-brand-primary/90')
  })

  it('should have proper focus styles', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('focus:outline-none')
    expect(button).toHaveClass('focus:ring-2')
    expect(button).toHaveClass('focus:ring-brand-primary/50')
  })

  it('should have active scale animation', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('active:scale-[0.98]')
  })

  it('should have transition effects', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')
    expect(button).toHaveClass('transition-all')
  })

  it('should meet minimum touch target size', () => {
    render(<FloatingCTAButton onClick={mockOnClick} />)

    const button = screen.getByRole('button')

    // The min-h-[56px] class ensures minimum height
    expect(button).toHaveClass('min-h-[56px]')
  })

  it('should be accessible with navigation role', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const wrapper = container.querySelector('[role="navigation"]')
    expect(wrapper).toBeInTheDocument()
    expect(wrapper).toHaveAttribute('aria-label', 'Primary actions')
  })

  it('should have max width constraint matching card width', () => {
    const { container } = render(<FloatingCTAButton onClick={mockOnClick} />)

    const innerContainer = container.querySelector('.max-w-lg')
    expect(innerContainer).toBeInTheDocument()
    expect(innerContainer).toHaveClass('mx-auto')
    expect(innerContainer).toHaveClass('w-full')
  })
})
