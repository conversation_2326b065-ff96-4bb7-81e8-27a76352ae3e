/**
 * Debug script to test the exercise recommendation API directly
 * This helps identify if the issue is with the API call or the UI
 */

import { getExerciseRecommendation } from '@/services/api/workout'
import { getUserSettings } from '@/services/userSettings'

export async function testRecommendationAPI() {
  console.log('🔍 Testing Exercise Recommendation API...')

  try {
    // Test user settings first
    console.log('📋 Getting user settings...')
    const userSettings = await getUserSettings()
    console.log('✅ User settings:', userSettings)

    // Test API call with exercise 27474 (Normal sets)
    console.log('🚀 Making API call for exercise 27474 (Normal sets)...')

    const normalRequest = {
      Username: '<EMAIL>', // Replace with actual user email
      ExerciseId: 27474,
      WorkoutId: 123, // Replace with actual workout ID
      SetStyle: 'Normal',
      IsFlexibility: false,
      IsQuickMode: userSettings.isQuickMode,
      LightSessionDays: userSettings.lightSessionDays,
      SwapedExId: undefined,
      IsStrengthPhashe: userSettings.isStrengthPhase,
      IsFreePlan: userSettings.isFreePlan,
      IsFirstWorkoutOfStrengthPhase: userSettings.isFirstWorkoutOfStrengthPhase,
      VersionNo: 1,
    }

    console.log('📤 Normal request:', normalRequest)
    const normalResult = await getExerciseRecommendation(normalRequest)
    console.log('📥 Normal result:', normalResult)

    // Test API call with RestPause sets
    console.log('🚀 Making API call for exercise 27474 (RestPause sets)...')

    const restPauseRequest = {
      ...normalRequest,
      SetStyle: 'RestPause',
    }

    console.log('📤 RestPause request:', restPauseRequest)
    const restPauseResult = await getExerciseRecommendation(restPauseRequest)
    console.log('📥 RestPause result:', restPauseResult)

    // Check results
    console.log('\n🔍 RESULTS ANALYSIS:')

    if (normalResult?.Weight) {
      console.log('✅ Normal endpoint - Weight found:', normalResult.Weight)
    } else {
      console.log('❌ Normal endpoint - No weight in response')
      console.log('🔍 Normal full object:', JSON.stringify(normalResult, null, 2))
    }

    if (restPauseResult?.Weight) {
      console.log('✅ RestPause endpoint - Weight found:', restPauseResult.Weight)
    } else {
      console.log('❌ RestPause endpoint - No weight in response')
      console.log('🔍 RestPause full object:', JSON.stringify(restPauseResult, null, 2))
    }

    return { normalResult, restPauseResult }
  } catch (error) {
    console.error('❌ API Test failed:', error)

    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    return null
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  ;(window as any).testRecommendationAPI = testRecommendationAPI
}
