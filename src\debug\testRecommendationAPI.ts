/**
 * Debug script to test the exercise recommendation API directly
 * This helps identify if the issue is with the API call or the UI
 */

import { getExerciseRecommendation } from '@/services/api/workout'
import { getUserSettings } from '@/services/userSettings'

export async function testRecommendationAPI() {
  console.log('🔍 Testing Exercise Recommendation API...')

  try {
    // Test user settings first
    console.log('📋 Getting user settings...')
    const userSettings = await getUserSettings()
    console.log('✅ User settings:', userSettings)

    // Test API call with exercise 27474
    console.log('🚀 Making API call for exercise 27474...')

    const request = {
      Username: '<EMAIL>', // Replace with actual user email
      ExerciseId: 27474,
      WorkoutId: 123, // Replace with actual workout ID
      SetStyle: 'Normal',
      IsFlexibility: false,
      IsQuickMode: userSettings.isQuickMode,
      LightSessionDays: userSettings.lightSessionDays,
      SwapedExId: undefined,
      IsStrengthPhashe: userSettings.isStrengthPhase,
      IsFreePlan: userSettings.isFreePlan,
      IsFirstWorkoutOfStrengthPhase: userSettings.isFirstWorkoutOfStrengthPhase,
      VersionNo: 1,
    }

    console.log('📤 Request payload:', request)

    const recommendation = await getExerciseRecommendation(request)

    console.log('📥 API Response:', recommendation)

    if (recommendation?.Weight) {
      console.log('✅ Weight found:', recommendation.Weight)
    } else {
      console.log('❌ No weight in response')
      console.log(
        '🔍 Full recommendation object:',
        JSON.stringify(recommendation, null, 2)
      )
    }

    return recommendation
  } catch (error) {
    console.error('❌ API Test failed:', error)

    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    return null
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  ;(window as any).testRecommendationAPI = testRecommendationAPI
}
