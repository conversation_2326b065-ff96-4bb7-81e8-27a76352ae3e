/**
 * User Settings Service
 * Provides user preferences and settings for workout recommendations
 */

import { userProfileA<PERSON> } from '@/api/userProfile'
import { logger } from '@/utils/logger'

export interface UserSettings {
  isQuickMode: boolean
  isStrengthPhase: boolean
  isFreePlan: boolean
  isFirstWorkoutOfStrengthPhase: boolean
  lightSessionDays: number | null
}

/**
 * Get user settings for workout recommendations
 * This combines data from user profile API and sensible defaults
 */
export async function getUserSettings(): Promise<UserSettings> {
  try {
    // Try to get user profile data
    await userProfileApi.getUserInfo()
    // TODO: Use userInfo?.Result to get actual user preferences when available

    // For now, we'll use sensible defaults since the web app doesn't have
    // a comprehensive settings system yet. These should be replaced with
    // actual user preferences when available.
    const settings: UserSettings = {
      // Default to false for quick mode - most users prefer normal workouts
      isQuickMode: false,

      // Default to false for strength phase - most users are in hypertrophy
      isStrengthPhase: false,

      // Default to false for free plan - assume users have subscription
      // This should be replaced with actual subscription status check
      isFreePlan: false,

      // Default to false - this should be calculated based on workout history
      isFirstWorkoutOfStrengthPhase: false,

      // Default to null - this should be calculated based on user's workout history
      lightSessionDays: null,
    }

    logger.debug('[UserSettings] Retrieved user settings:', settings)
    return settings
  } catch (error) {
    logger.error(
      '[UserSettings] Failed to get user settings, using defaults:',
      error
    )

    // Return safe defaults if API call fails
    return {
      isQuickMode: false,
      isStrengthPhase: false,
      isFreePlan: false,
      isFirstWorkoutOfStrengthPhase: false,
      lightSessionDays: null,
    }
  }
}

/**
 * Get user settings synchronously from cache/localStorage
 * This is a fallback for when async settings aren't available
 */
export function getUserSettingsSync(): UserSettings {
  // For now, return the same defaults
  // In the future, this could read from localStorage or a cache
  return {
    isQuickMode: false,
    isStrengthPhase: false,
    isFreePlan: false,
    isFirstWorkoutOfStrengthPhase: false,
    lightSessionDays: null,
  }
}

/**
 * Calculate light session days based on user's workout history
 * This is a placeholder implementation that should be enhanced
 */
export function calculateLightSessionDays(): number | null {
  // TODO: Implement logic to calculate light session days based on:
  // - User's recent workout performance
  // - Recovery metrics
  // - Workout frequency
  // - User's age and fitness level

  // For now, return null (no light sessions)
  return null
}

/**
 * Determine if this is the first workout of a strength phase
 * This is a placeholder implementation that should be enhanced
 */
export function isFirstWorkoutOfStrengthPhase(): boolean {
  // TODO: Implement logic to determine if this is the first workout
  // of a strength phase based on:
  // - User's workout history
  // - Program progression
  // - Phase transitions

  // For now, return false
  return false
}
