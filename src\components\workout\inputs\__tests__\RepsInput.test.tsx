import React from 'react'
import { render, screen } from '@testing-library/react'
import { describe, expect, it, vi } from 'vitest'
import { RepsInput } from '../RepsInput'

describe('RepsInput', () => {
  const defaultProps = {
    reps: 10,
    onChange: vi.fn(),
  }

  it('renders with theme-aware text colors for label', () => {
    render(<RepsInput {...defaultProps} />)

    const label = screen.getByText('Reps')
    expect(label).toHaveClass('text-text-secondary')
  })

  it('renders input with theme-aware text and border colors', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('text-text-primary')
    expect(input).toHaveClass('border-text-tertiary')
    expect(input).toHaveClass('bg-bg-secondary')
  })

  it('renders quick buttons with theme-aware colors', () => {
    render(<RepsInput {...defaultProps} />)

    const quickButtons = screen.getAllByRole('button')
    quickButtons.forEach((button) => {
      if (button.textContent === '10') {
        // Selected button
        expect(button).toHaveClass('bg-brand-primary')
        expect(button).toHaveClass('text-text-inverse')
        expect(button).toHaveClass('border-brand-primary')
      } else {
        // Unselected buttons
        expect(button).toHaveClass('bg-bg-secondary')
        expect(button).toHaveClass('text-text-primary')
        expect(button).toHaveClass('border-text-tertiary')
      }
    })
  })

  it('maintains focus styles with theme colors', () => {
    render(<RepsInput {...defaultProps} />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    expect(input).toHaveClass('focus:ring-brand-primary')
  })

  it('shows error state with semantic error color', () => {
    render(<RepsInput {...defaultProps} error="Invalid reps" />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    const errorText = screen.getByRole('alert')

    expect(input).toHaveClass('border-error')
    expect(errorText).toHaveClass('text-error')
  })

  it('shows disabled state with theme-aware colors', () => {
    render(<RepsInput {...defaultProps} disabled />)

    const input = screen.getByRole('spinbutton', { name: 'Reps' })
    const quickButtons = screen.getAllByRole('button')

    expect(input).toHaveClass('bg-bg-tertiary')
    expect(input).toHaveClass('text-text-tertiary')

    quickButtons.forEach((button) => {
      expect(button).toHaveClass('text-text-tertiary')
    })
  })
})
