import { useEffect, useMemo, useCallback } from 'react'
import { useWorkoutStore } from '@/stores/workoutStore/index'
import { useAuthStore } from '@/stores/authStore'
import { logger } from '@/utils/logger'

// Import split hooks
import { useWorkoutState } from './useWorkoutState'
import { useWorkoutActions } from './useWorkoutActions'
import { useWorkoutSync } from './useWorkoutSync'
import { useWorkoutDataLoader } from './useWorkoutDataLoader'
import { useWorkoutRecommendations } from './useWorkoutRecommendations'
import { hasValidCachedSets } from '@/types/workout'
import type { RecommendationModel } from '@/types'

export function useWorkout() {
  const { isAuthenticated } = useAuthStore()
  const {
    getCachedUserProgramInfo,
    getCachedUserWorkouts,
    getCachedTodaysWorkout,
    getCurrentExercise,
    getNextExercise,
    getRestDuration,
    getCacheStats,
    getExerciseProgress,
    loadAllExerciseRecommendations,
    loadingStates,
    updateSetRIR,
    getCachedExerciseRecommendation,
    isCacheStale,
  } = useWorkoutStore()

  // Use split hooks
  const state = useWorkoutState()
  const actions = useWorkoutActions()
  const sync = useWorkoutSync()
  const dataLoader = useWorkoutDataLoader()
  const recommendations = useWorkoutRecommendations()

  // Progressive loading effect - load sets for visible exercises
  useEffect(() => {
    if (
      !isAuthenticated ||
      state.isOffline ||
      state.exerciseWorkSetsModels.length === 0 ||
      !state.currentWorkout
    ) {
      return
    }

    // Load sets for exercises that haven't been loaded yet
    const loadSetsForExercises = async () => {
      const exercisesToLoad = state.exerciseWorkSetsModels.filter(
        (exercise) => {
          // Skip if already loaded or currently loading
          if (state.loadedExerciseSets.current.has(exercise.Id)) return false

          // Skip if we have valid cached sets
          if (hasValidCachedSets(exercise)) return false

          return true
        }
      )

      if (exercisesToLoad.length === 0) return

      // Mark exercises as loading
      exercisesToLoad.forEach((exercise) => {
        state.loadedExerciseSets.current.add(exercise.Id)
      })

      // Load sets using the data loader
      await dataLoader.loadExerciseSets(
        exercisesToLoad.map((e) => e.Id),
        state.exerciseWorkSetsModels,
        state.setExerciseWorkSetsModels
      )
    }

    loadSetsForExercises()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    state.exerciseWorkSetsModels.length,
    isAuthenticated,
    state.isOffline,
    state.currentWorkout,
  ])

  // Preload recommendations when workout starts
  useEffect(() => {
    if (state.currentWorkout && state.exercises.length > 0) {
      recommendations.preloadRecommendations(
        state.exercises.map((e) => ({ Id: e.Id, Label: e.Label }))
      )
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.currentWorkout])

  // Sync pending sets when coming back online
  useEffect(() => {
    if (!state.isOffline && state.workoutSession) {
      sync.syncPendingSets()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [state.isOffline])

  // Combined loading state
  const isLoading = state.isLoading || dataLoader.isLoading

  // Memoized queries for cached data
  const cachedUserProgramInfo = useMemo(
    () => getCachedUserProgramInfo(),
    [getCachedUserProgramInfo]
  )

  const cachedUserWorkouts = useMemo(
    () => getCachedUserWorkouts(),
    [getCachedUserWorkouts]
  )

  const cachedTodaysWorkout = useMemo(
    () => getCachedTodaysWorkout(),
    [getCachedTodaysWorkout]
  )

  // Get exercise progress for derived properties
  const exerciseProgress = useMemo(() => {
    return getExerciseProgress()
  }, [getExerciseProgress])

  // Derived properties
  const totalSets = exerciseProgress?.totalSets || 0
  const isLastSet = state.currentSetIndex >= totalSets - 1
  const isLastExercise =
    state.currentExerciseIndex >= state.exercises.length - 1

  // Create backward compatibility functions
  const finishWorkout = useCallback(async () => {
    return actions.completeWorkout()
  }, [actions])

  const goToNextExercise = useCallback(() => {
    actions.nextExercise()
  }, [actions])

  const refreshWorkout = useCallback(async () => {
    await dataLoader.refetchAll()
  }, [dataLoader])

  const updateExerciseWorkSets = useCallback(
    (exerciseId: number, sets: unknown[]) => {
      state.setExerciseWorkSetsModels((prev) =>
        prev.map((exercise) => {
          if (exercise.Id === exerciseId) {
            return {
              ...exercise,
              WorkoutSets: sets,
              isLoading: false,
              error: null,
            }
          }
          return exercise
        })
      )
    },
    [state]
  )

  // Simple getRecommendation function
  const getRecommendation = useCallback(
    async (exerciseId: number): Promise<RecommendationModel | null> => {
      // Check cache first
      const cached = getCachedExerciseRecommendation(exerciseId)
      const isStale = isCacheStale('exerciseRecommendation', exerciseId)

      if (cached !== undefined && !isStale) {
        return cached
      }

      // Use the recommendations hook to load
      return recommendations.loadRecommendation(exerciseId, 'Exercise')
    },
    [getCachedExerciseRecommendation, isCacheStale, recommendations]
  )

  // Dedicated exercise recommendation loading function following mobile app pattern
  const loadExerciseRecommendation = useCallback(
    async (exerciseId: number): Promise<RecommendationModel | null> => {
      try {
        // Get the exercise from the store
        const exercise = state.exerciseWorkSetsModels?.find(
          (ex) => ex.Id === exerciseId
        )
        if (!exercise) {
          logger.warn(`Exercise ${exerciseId} not found in workout`)
          return null
        }

        // Use the recommendations hook to load with proper exercise name
        const recommendation = await recommendations.loadRecommendation(
          exerciseId,
          exercise.Label || 'Exercise'
        )

        if (recommendation) {
          // Update the exercise work sets if needed
          updateExerciseWorkSets(exerciseId, exercise.sets || [])
        }

        return recommendation
      } catch (error) {
        console.error(
          `Error loading recommendation for exercise ${exerciseId}:`,
          error
        )
        throw error
      }
    },
    [state.exerciseWorkSetsModels, recommendations, updateExerciseWorkSets]
  )

  // Backward compatibility mappings
  const isLoadingWorkout = dataLoader.isLoading || state.isLoading
  const workoutError =
    dataLoader.userProgramInfoQuery.error ||
    dataLoader.userWorkoutsQuery.error ||
    dataLoader.todaysWorkoutQuery.error ||
    state.error

  return {
    // State
    currentWorkout: state.currentWorkout,
    currentExercise: state.currentExercise,
    currentExerciseIndex: state.currentExerciseIndex,
    currentSetIndex: state.currentSetIndex,
    exercises: state.exerciseWorkSetsModels,
    workoutSession: state.workoutSession,
    isLoading,
    error: state.error,
    isOffline: state.isOffline,
    cachedWorkout: state.cachedWorkout,

    // Actions
    startWorkout: actions.startWorkout,
    nextSet: actions.nextSet,
    nextExercise: actions.nextExercise,
    completeWorkout: actions.completeWorkout,
    isCompletingWorkout: actions.isCompletingWorkout,
    saveSet: sync.saveSet,
    finishWorkout, // Added for WorkoutComplete
    goToNextExercise, // Added for WorkoutScreen

    // Data
    userProgramInfo:
      dataLoader.optimisticData.userProgramInfo || cachedUserProgramInfo,
    userWorkouts: dataLoader.optimisticData.userWorkouts || cachedUserWorkouts,
    todaysWorkout:
      dataLoader.optimisticData.todaysWorkout || cachedTodaysWorkout,

    // Queries
    userProgramInfoQuery: dataLoader.userProgramInfoQuery,
    userWorkoutsQuery: dataLoader.userWorkoutsQuery,
    todaysWorkoutQuery: dataLoader.todaysWorkoutQuery,
    refetchAll: dataLoader.refetchAll,

    // Recommendations
    loadRecommendation: recommendations.loadRecommendation,
    preloadRecommendations: recommendations.preloadRecommendations,
    invalidateRecommendation: recommendations.invalidateRecommendation,
    getRecommendation, // Added for WorkoutScreen
    loadExerciseRecommendation, // Added for exercise page loading
    loadAllExerciseRecommendations:
      recommendations.loadAllExerciseRecommendations ||
      loadAllExerciseRecommendations,

    // Utility functions
    getCurrentExercise,
    getNextExercise,
    getRestDuration,
    getCacheStats,
    getExerciseProgress,

    // Derived properties
    totalSets,
    isLastSet,
    isLastExercise,

    // WorkoutOverview specific properties
    isLoadingWorkout, // Added for WorkoutOverview
    workoutError, // Added for error handling
    exerciseWorkSetsModels: state.exerciseWorkSetsModels,
    loadingStates, // From store
    expectedExerciseCount: state.exercises?.length || 0,
    hasInitialData:
      !!cachedTodaysWorkout || !!dataLoader.optimisticData.todaysWorkout,
    isLoadingFresh:
      dataLoader.userProgramInfoQuery.isFetching ||
      dataLoader.userWorkoutsQuery.isFetching,
    refreshWorkout, // Added for pull-to-refresh
    updateExerciseWorkSets, // Added for WorkoutOverview
    updateSetRIR, // From store
  }
}
