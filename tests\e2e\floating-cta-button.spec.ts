import { test, expect } from '@playwright/test'

test.describe('Floating CTA Button', () => {
  test('should display floating CTA button on program page', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button:has-text("Sign In")')

    // Wait for navigation to program page
    await page.waitForURL('/program')

    // Check floating CTA button is present
    const floatingButton = page.locator(
      '[data-testid="floating-cta-container"]'
    )
    await expect(floatingButton).toBeVisible()

    // Verify button positioning
    await expect(floatingButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50/
    )

    // Verify button text
    const button = floatingButton.locator('button')
    await expect(button).toHaveText('Open Workout')

    // Verify button styling
    await expect(button).toHaveClass(/rounded-full/)
    await expect(button).toHaveClass(/bg-brand-primary/)
  })

  test('should display floating CTA button on workout page', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button:has-text("Sign In")')

    // Navigate to workout page
    await page.goto('/workout')

    // Check floating CTA button is present
    const floatingButton = page.locator(
      '[data-testid="floating-cta-container"]'
    )
    await expect(floatingButton).toBeVisible()

    // Verify button text (should be "Start Workout" initially)
    const button = floatingButton.locator('button')
    await expect(button).toHaveText('Start Workout')

    // Verify consistent styling
    await expect(button).toHaveClass(/rounded-full/)
    await expect(floatingButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50/
    )
  })

  test('should display floating CTA button on workout complete page', async ({
    page,
  }) => {
    // Login first
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button:has-text("Sign In")')

    // Navigate to workout complete page
    await page.goto('/workout/complete')

    // Check floating CTA button is present
    const floatingButton = page.locator(
      '[data-testid="floating-cta-container"]'
    )
    await expect(floatingButton).toBeVisible()

    // Verify button text
    const button = floatingButton.locator('button')
    await expect(button).toHaveText('Back to Home')

    // Verify consistent positioning and styling
    await expect(floatingButton).toHaveClass(
      /fixed bottom-6 left-0 right-0 z-50/
    )
    await expect(button).toHaveClass(/rounded-full/)
  })

  test('floating CTA button should be clickable on mobile viewport', async ({
    page,
  }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Login
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button:has-text("Sign In")')

    // Wait for program page
    await page.waitForURL('/program')

    // Click floating CTA button
    const button = page.locator('[data-testid="floating-cta-container"] button')
    await expect(button).toBeVisible()
    await button.click()

    // Should navigate to workout page
    await page.waitForURL('/workout')
  })

  test('floating CTA button should match stat card width', async ({ page }) => {
    // Login
    await page.goto('/login')
    await page.fill('[name="email"]', '<EMAIL>')
    await page.fill('[name="password"]', 'Dr123456')
    await page.click('button:has-text("Sign In")')

    // Wait for program page
    await page.waitForURL('/program')

    // Get stat card container width
    const statCard = page.locator('.max-w-lg').first()
    const statCardBox = await statCard.boundingBox()

    // Get floating button container width
    const floatingContainer = page.locator(
      '[data-testid="floating-cta-container"] .max-w-lg'
    )
    const floatingBox = await floatingContainer.boundingBox()

    // Verify widths match
    if (statCardBox && floatingBox) {
      expect(Math.abs(statCardBox.width - floatingBox.width)).toBeLessThan(1)
    }

    // Verify centered positioning
    const viewportSize = page.viewportSize()
    if (floatingBox && viewportSize) {
      const expectedX = (viewportSize.width - floatingBox.width) / 2
      expect(Math.abs(floatingBox.x - expectedX)).toBeLessThan(20) // Allow small margin
    }
  })
})
